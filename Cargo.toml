[package]
name = "labitbu"
version = "0.1.0"
edition = "2021"

[package.metadata.wasm-pack.profile.release]
wasm-opt = ["-Oz", "--enable-bulk-memory", "--enable-nontrapping-float-to-int"]

[dependencies]
wasm-bindgen = "0.2.100"
serde-wasm-bindgen = "0.6.5"
bitcoin = { version = "0.32.6", default-features = false, features = [
    "std",
    "serde",
    "secp-recovery",
    "rand",
] }
secp256k1 = { version = "0.29.0", features = [
    "global-context",
    "serde",
    "rand",
] }
image = "0.24"
image-webp = "0.2.3"
hex = "0.4.3"
rand = { version = "0.8", features = ["small_rng"] }
getrandom = { version = "0.2", features = ["js"] }

[dev-dependencies]
wasm-bindgen-test = "0.3"

[lib]
crate-type = ["cdylib"]


[profile.release]
strip = true
lto = true
opt-level = 'z'
codegen-units = 1
panic = "abort"
incremental = false
