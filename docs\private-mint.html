<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" />
  <title>私钥铸造 - labitbu.com 😴</title>

  <style>
    body{font-family:system-ui,sans-serif;margin:0;line-height:1.4;background:#fff!important}
    #labitbu-container{max-width:800px;margin:40px auto;padding:0 1rem}
    button{background:#007bff;color:#fff;border:none;padding:10px 15px;border-radius:5px;cursor:pointer;font-size:16px;margin:5px}
    button:hover{background:#0056b3}
    button:disabled{background:#6c757d!important;cursor:default;opacity:.55}
    button.danger{background:#dc3545}
    button.danger:hover{background:#c82333}
    #beta-banner{background:#fff3cd;color:#856404;padding:.75rem 1rem;border-bottom:1px solid #ffeeba;font-size:.85rem;text-align:center}
    #security-warning{background:#f8d7da;color:#721c24;padding:1rem;border:1px solid #f5c6cb;border-radius:5px;margin:1rem 0;font-size:.9rem}
    .input-group{margin:1rem 0}
    .input-group label{display:block;font-weight:bold;margin-bottom:.5rem;color:#333}
    .input-group input, .input-group textarea{width:100%;padding:10px;font-size:16px;border:1px solid #ccc;border-radius:5px;box-sizing:border-box}
    .input-group textarea{min-height:100px;resize:vertical}
    .error{color:#dc3545;font-size:.9rem;margin-top:.5rem}
    .success{color:#28a745;font-size:.9rem;margin-top:.5rem}
    .info{color:#17a2b8;font-size:.9rem;margin-top:.5rem}
    #wallet-info{background:#e9ecef;padding:1rem;border-radius:5px;margin:1rem 0}
    #destination-address{width:100%;padding:10px;font-size:16px;border:1px solid #ccc;border-radius:5px;box-sizing:border-box;margin-top:.5rem}
    .batch-section{background:#f8f9fa;padding:1rem;border-radius:5px;margin:1rem 0}
    .batch-controls{display:flex;gap:10px;align-items:center;margin:10px 0}
    .batch-controls input[type="number"]{width:100px}
    #batch-progress{margin:10px 0}
    .progress-bar{width:100%;height:20px;background:#e9ecef;border-radius:10px;overflow:hidden}
    .progress-fill{height:100%;background:#28a745;transition:width 0.3s}
    .nav-links{text-align:center;margin:2rem 0}
    .nav-links a{color:#007bff;text-decoration:none;margin:0 1rem}
    .nav-links a:hover{text-decoration:underline}
    .key-input-tabs{display:flex;margin-bottom:1rem}
    .key-input-tabs button{margin:0;border-radius:5px 5px 0 0}
    .key-input-tabs button.active{background:#28a745}
    .tab-content{display:none}
    .tab-content.active{display:block}
    #labitbu-preview{text-align:center;margin:1rem 0}
    #labitbu-preview img{max-width:200px;border:2px solid #dee2e6;border-radius:10px}
  </style>
</head>
<body>

  <div id="labitbu-container">
    <h1 style="text-align:center;color:#333;">私钥铸造 labitbu.com 😴</h1>

    <div class="nav-links">
      <a href="index.html">← 返回钱包连接版本</a>
      <a href="https://github.com/stutxo/labitbu" target="_blank">GitHub</a>
    </div>

    <!-- 密钥输入选项卡 -->
    <div class="key-input-tabs">
      <button id="tab-mnemonic" class="active" type="button">助记词</button>
      <button id="tab-private-key" type="button">私钥</button>
    </div>

    <!-- 助记词输入 -->
    <div id="tab-content-mnemonic" class="tab-content active">
      <div class="input-group">
        <label>助记词 (12 或 24 个单词)</label>
        <textarea id="mnemonic-input" placeholder="输入你的 BIP39 助记词，用空格分隔..."></textarea>
        <div class="error" id="mnemonic-error"></div>
      </div>
      
      <div class="input-group">
        <label>派生路径 (可选)</label>
        <select id="derivation-path-select" onchange="updateDerivationPath()">
          <option value="m/84'/0'/0'/0/0">m/84'/0'/0'/0/0 (P2WPKH - 大多数钱包)</option>
          <option value="m/86'/0'/0'/0/0">m/86'/0'/0'/0/0 (Taproot - BIP86)</option>
          <option value="m/44'/0'/0'/0/0">m/44'/0'/0'/0/0 (Legacy - BIP44)</option>
          <option value="custom">自定义路径</option>
        </select>
        <input type="text" id="derivation-path" value="m/84'/0'/0'/0/0" placeholder="m/84'/0'/0'/0/0" style="display:none;margin-top:10px;" />
        <div class="info">选择与你的 Xverse 钱包相同的派生路径以生成相同的 Labitbu</div>
      </div>
    </div>

    <!-- 私钥输入 -->
    <div id="tab-content-private-key" class="tab-content">
      <div class="input-group">
        <label>私钥 (WIF 格式或十六进制)</label>
        <input type="text" id="private-key-input" placeholder="输入 WIF 格式私钥或 64 字符十六进制私钥..." />
        <div class="error" id="private-key-error"></div>
        <div class="info">支持 WIF 格式 (以 K, L 或 5 开头) 或 64 字符十六进制格式</div>
      </div>
    </div>

    <!-- 测试模式和设置 -->
    <div class="settings-section" style="background:#f8f9fa;padding:1rem;border-radius:8px;margin:1rem 0;">
      <h4>🔧 设置选项</h4>
      <div style="display:flex;gap:2rem;align-items:center;flex-wrap:wrap;">
        <label style="display:flex;align-items:center;gap:0.5rem;">
          <input type="checkbox" id="test-mode-checkbox" />
          <span>🧪 测试模式 (使用虚拟UTXO)</span>
        </label>
        <div style="display:flex;align-items:center;gap:0.5rem;">
          <label>⚡ 费率 (sat/vB):</label>
          <input type="number" id="custom-fee-rate" value="20" min="1" max="1000" step="0.01" style="width:80px;" />
          <button id="refresh-fee-btn" style="padding:4px 8px;font-size:12px;">获取推荐</button>
        </div>
      </div>
      <div id="test-mode-info" style="display:none;margin-top:0.5rem;padding:0.5rem;background:#e7f3ff;border-radius:4px;font-size:14px;">
        📌 测试模式已开启 - 将使用虚拟UTXO进行测试，不会真实广播交易
      </div>
    </div>

    <!-- 生成钱包按钮 -->
    <div style="text-align:center;margin:2rem 0">
      <button id="generate-wallet-btn">生成钱包信息</button>
      <button id="clear-inputs-btn" class="danger">清除输入</button>
    </div>

    <!-- 钱包信息显示 -->
    <div id="wallet-info" style="display:none">
      <h3>钱包信息</h3>
      <p><strong>派生路径：</strong><span id="derivation-path-display"></span></p>
      <p><strong>公钥：</strong><span id="wallet-pubkey"></span></p>
      <p><strong>地址：</strong><span id="wallet-address"></span></p>
      <p><strong>Labitbu 存款地址：</strong><span id="labitbu-address"></span></p>
      <div id="labitbu-preview"></div>
    </div>

    <!-- 单次铸造部分 -->
    <div id="single-mint-section" style="display:none">
      <h3>单次铸造</h3>
      <div class="input-group">
        <label>目标地址</label>
        <input id="destination-address" type="text" placeholder="bc1p... Taproot 地址" />
        <div class="error" id="address-error"></div>
        <div class="info">⚠️ 需要支持 Ordinals 的钱包 (FIFO)</div>
      </div>
      
      <div style="text-align:center;margin:1rem 0">
        <button id="check-balance-btn">检查余额</button>
        <button id="single-mint-btn" disabled>铸造单个 Labitbu</button>
      </div>
      <div id="single-mint-status"></div>
    </div>

    <!-- 批量铸造部分 -->
    <div id="batch-mint-section" class="batch-section" style="display:none">
      <h3>批量铸造</h3>
      
      <!-- 多地址批量铸造设置 -->
      <div style="margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 5px; border-left: 4px solid #007bff;">
        <p style="margin: 5px 0; color: #0056b3;"><strong>💡 批量铸造说明:</strong> 从助记词派生多个地址，每个地址铸造不同的Labitbu图像 (仅助记词模式可用)</p>
      </div>

      <div class="batch-controls">
        <label>派生地址数量：</label>
        <input type="number" id="derivation-count" min="1" max="500" value="5" />
        <button onclick="scanDerivedAddresses()" style="background: #17a2b8; color: white; padding: 8px 15px; border: none; border-radius: 3px; margin-left: 10px;">
          📡 扫描派生地址
        </button>
        <button id="start-multi-batch-btn" disabled style="background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 3px; margin-left: 10px;">
          🚀 开始批量铸造
        </button>
      </div>
      
      <div id="derived-addresses-status" style="margin-top: 10px;"></div>
      
      <div class="input-group">
        <label>批量目标地址</label>
        <textarea id="multi-batch-addresses" placeholder="每行一个 bc1p... 地址，或留空使用单一地址"></textarea>
        <div class="info">派生地址将各自铸造不同的Labitbu到对应目标地址</div>
      </div>
      
      <div id="batch-progress" style="display:none">
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div id="batch-status"></div>
      </div>
      
      <div id="batch-results"></div>
    </div>

    <div id="transaction-history" style="margin-top:2rem"></div>
  </div>

  <!-- 添加 Buffer polyfill -->
  <script>
    window.global = window;
    window.process = { env: {} };
    // 预先加载 Buffer polyfill
    if (typeof globalThis === 'undefined') {
      window.globalThis = window;
    }
  </script>
  
  <script type="module">
    import { Buffer } from 'https://esm.sh/buffer';
    
    // 设置全局 Buffer 和其他必要的全局变量
    window.Buffer = Buffer;
    globalThis.Buffer = Buffer;
    
    // 工具函数
    function hexToBytes(hex) {
      const out = new Uint8Array(hex.length / 2);
      for (let i = 0; i < hex.length; i += 2) {
        out[i / 2] = parseInt(hex.substr(i, 2), 16);
      }
      return out;
    }

    // 先加载数据
    const { Lasnoozesnooze, traits } = await (await fetch('/lasnoozesnooze.json')).json();
    const sleepyBase = Object.values(Lasnoozesnooze).map(hexToBytes);
    const sleepyAcc = Object.values(traits).map(hexToBytes);

    // 先初始化我们自己的 WASM 模块
    const { default: initWasm, create_deposit_address, mint, generate_labitbu_bytes } = 
      await import('./pkg/labitbu.js');
    
    console.log('开始初始化 Labitbu WASM 模块...');
    await initWasm();
    console.log('Labitbu WASM 模块初始化完成');

    // 延迟加载其他依赖模块，确保所有 WASM 都已初始化
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('开始加载其他依赖模块...');
    
    // 使用专门为 BitcoinJS 设计的无 WASM 依赖的 ECC 库
    const [
      { default: mempoolJS },
      bitcoin,
      bip39,
      ecc,
      { BIP32Factory },
      { ECPairFactory },
      wif // 添加 wif 模块
    ] = await Promise.all([
      import('https://esm.sh/@mempool/mempool.js'),
      import('https://esm.sh/bitcoinjs-lib@6.1.3'),
      import('https://esm.sh/bip39@3.1.0'),
      import('https://esm.sh/@bitcoinerlab/secp256k1@1.0.5'),
      import('https://esm.sh/bip32@4.0.0'),
      import('https://esm.sh/ecpair@2.1.0'),
      import('https://esm.sh/wif@5.0.0') // 独立的 wif 模块
    ]);
    
    console.log('所有模块加载完成');

    // 初始化 bitcoin-js 工厂
    bitcoin.initEccLib(ecc);
    const bip32 = BIP32Factory(ecc);
    const ECPair = ECPairFactory(ecc);

    const { bitcoin: { addresses, transactions, fees } } = mempoolJS({ hostname: 'mempool.space' });

    // DOM 元素
    const mnemonicInput = document.getElementById('mnemonic-input');
    const privateKeyInput = document.getElementById('private-key-input');
    const derivationPathInput = document.getElementById('derivation-path');
    const generateWalletBtn = document.getElementById('generate-wallet-btn');
    const clearInputsBtn = document.getElementById('clear-inputs-btn');
    const walletInfo = document.getElementById('wallet-info');
    const singleMintSection = document.getElementById('single-mint-section');
    const batchMintSection = document.getElementById('batch-mint-section');
    const destinationAddress = document.getElementById('destination-address');
    const checkBalanceBtn = document.getElementById('check-balance-btn');
    const singleMintBtn = document.getElementById('single-mint-btn');

    // 全局状态
    let currentWallet = null;
    let currentLabitbuBytes = null;
    let currentDepositAddress = null;
    let batchMinting = false;
    let testMode = false;
    let customFeeRate = 1;

    // LocalStorage 数据管理
    const STORAGE_KEYS = {
      MNEMONIC: 'labitbu_mnemonic',
      DERIVATION_PATH: 'labitbu_derivation_path',
      DESTINATION_ADDRESS: 'labitbu_destination_address',
      TEST_MODE: 'labitbu_test_mode',
      CUSTOM_FEE_RATE: 'labitbu_custom_fee_rate',
      TRANSACTION_HISTORY: 'labitbu_transaction_history'
    };

    function saveToStorage(key, value) {
      try {
        localStorage.setItem(key, value);
      } catch (e) {
        console.warn('无法保存到 localStorage:', e);
      }
    }

    function loadFromStorage(key, defaultValue = '') {
      try {
        return localStorage.getItem(key) || defaultValue;
      } catch (e) {
        console.warn('无法从 localStorage 读取:', e);
        return defaultValue;
      }
    }

    function clearStorage() {
      try {
        Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
      } catch (e) {
        console.warn('无法清除 localStorage:', e);
      }
    }

    // 虚拟UTXO生成 (测试模式用)
    function generateMockUTXOs(address, count = 5) {
      const utxos = [];
      for (let i = 0; i < count; i++) {
        utxos.push({
          txid: '0'.repeat(64), // 虚假的交易ID
          vout: i,
          value: 500000 + (i * 100000), // 0.005 到 0.009 BTC，提供更多余额
          confirmed: true,
          // 添加虚拟的 scriptPubKey (P2TR格式)
          scriptPubKey: '51201234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef'
        });
      }
      return utxos;
    }

    // 加载保存的数据
    function loadSavedData() {
      // 加载助记词
      const savedMnemonic = loadFromStorage(STORAGE_KEYS.MNEMONIC);
      if (savedMnemonic) {
        mnemonicInput.value = savedMnemonic;
      }

      // 加载派生路径
      const savedPath = loadFromStorage(STORAGE_KEYS.DERIVATION_PATH, 'm/84\'/0\'/0\'/0/0');
      document.getElementById('derivation-path').value = savedPath;
      const pathSelect = document.getElementById('derivation-path-select');
      if (pathSelect.querySelector(`option[value="${savedPath}"]`)) {
        pathSelect.value = savedPath;
      } else {
        pathSelect.value = 'custom';
        document.getElementById('derivation-path').style.display = 'block';
      }

      // 加载目标地址
      const savedDestination = loadFromStorage(STORAGE_KEYS.DESTINATION_ADDRESS);
      if (savedDestination && destinationAddress) {
        destinationAddress.value = savedDestination;
      }

      // 加载测试模式
      const savedTestMode = loadFromStorage(STORAGE_KEYS.TEST_MODE) === 'true';
      document.getElementById('test-mode-checkbox').checked = savedTestMode;
      testMode = savedTestMode;
      document.getElementById('test-mode-info').style.display = testMode ? 'block' : 'none';

      // 加载自定义费率
      const savedFeeRate = loadFromStorage(STORAGE_KEYS.CUSTOM_FEE_RATE, '1');
      document.getElementById('custom-fee-rate').value = savedFeeRate;
      customFeeRate = parseFloat(savedFeeRate);
    }

    function showError(elementId, message) {
      document.getElementById(elementId).textContent = message;
    }

    function showSuccess(elementId, message) {
      document.getElementById(elementId).innerHTML = `<span style="color: #28a745">${message}</span>`;
    }

    function clearErrors() {
      document.querySelectorAll('.error').forEach(el => el.textContent = '');
    }

    // 选项卡切换
    function switchTab(tabName) {
      console.log('切换到选项卡:', tabName);
      
      // 隐藏所有选项卡内容
      const tabContents = document.querySelectorAll('.tab-content');
      const tabButtons = document.querySelectorAll('.key-input-tabs button');
      
      tabContents.forEach(tab => tab.classList.remove('active'));
      tabButtons.forEach(btn => btn.classList.remove('active'));

      // 显示选中的选项卡
      const targetContent = document.getElementById(`tab-content-${tabName}`);
      const targetButton = document.getElementById(`tab-${tabName}`);
      
      if (targetContent) targetContent.classList.add('active');
      if (targetButton) targetButton.classList.add('active');
      
      console.log('选项卡切换完成');
    }

    // 初始化事件绑定
    function initializeEventListeners() {
      console.log('其他事件监听器初始化完成');
      // 这里可以添加其他需要的事件监听器
    }

    // 从助记词生成钱包
    function generateWalletFromMnemonic(mnemonic, path) {
      try {
        if (!bip39.validateMnemonic(mnemonic)) {
          throw new Error('无效的助记词');
        }

        const seed = bip39.mnemonicToSeedSync(mnemonic);
        const root = bip32.fromSeed(seed);
        const child = root.derivePath(path);
        
        if (!child.privateKey) {
          throw new Error('无法派生私钥');
        }

              const keyPair = ECPair.fromPrivateKey(child.privateKey, { compressed: true });
      
      // 根据派生路径生成对应类型的地址
      let address;
      if (path.startsWith("m/44'")) {
        // Legacy P2PKH (1xxx地址)
        address = bitcoin.payments.p2pkh({ pubkey: keyPair.publicKey }).address;
      } else if (path.startsWith("m/84'")) {
        // Native SegWit P2WPKH (bc1qxxx地址)
        address = bitcoin.payments.p2wpkh({ pubkey: keyPair.publicKey }).address;
      } else if (path.startsWith("m/86'")) {
        // Taproot P2TR (bc1pxxx地址)
        address = bitcoin.payments.p2tr({ pubkey: keyPair.publicKey.slice(1) }).address;
      } else {
        // 默认使用 P2WPKH
        address = bitcoin.payments.p2wpkh({ pubkey: keyPair.publicKey }).address;
      }
      
      return {
        privateKey: keyPair.privateKey,
        publicKey: keyPair.publicKey.slice(1), // 移除压缩标志获取 X-only 公钥（用于 Labitbu 生成）
        fullPublicKey: keyPair.publicKey, // 保留完整公钥（用于地址生成）
        address: address
      };
      } catch (error) {
        throw new Error(`助记词解析失败: ${error.message}`);
      }
    }

    // 从私钥生成钱包
    function generateWalletFromPrivateKey(privateKeyStr) {
      try {
        let keyPair;
        
        if (privateKeyStr.length === 64) {
          // 十六进制格式
          const privateKey = Buffer.from(privateKeyStr, 'hex');
          keyPair = ECPair.fromPrivateKey(privateKey, { compressed: true });
        } else {
          // WIF 格式 - 先解码WIF获取私钥，再创建keyPair
          const decoded = wif.decode(privateKeyStr);
          keyPair = ECPair.fromPrivateKey(Buffer.from(decoded.privateKey), { 
            compressed: decoded.compressed 
          });
        }
      
      // 对于私钥，我们默认生成 P2WPKH 地址（bc1q，与大多数钱包兼容）
      const address = bitcoin.payments.p2wpkh({ pubkey: keyPair.publicKey }).address;
      
      return {
        privateKey: keyPair.privateKey,
        publicKey: keyPair.publicKey.slice(1), // X-only 公钥（用于 Labitbu 生成）
        fullPublicKey: keyPair.publicKey, // 保留完整公钥（用于地址生成）
        address: address
      };
      } catch (error) {
        throw new Error(`私钥解析失败: ${error.message}`);
      }
    }

    // 生成钱包信息
    generateWalletBtn.onclick = async () => {
      clearErrors();
      
      try {
        const activeTab = document.querySelector('.tab-content.active');
        let wallet;

        if (activeTab.id === 'tab-content-mnemonic') {
          const mnemonic = mnemonicInput.value.trim();
          const path = derivationPathInput.value.trim();
          
          if (!mnemonic) {
            showError('mnemonic-error', '请输入助记词');
            return;
          }
          
          wallet = generateWalletFromMnemonic(mnemonic, path);
        } else {
          const privateKeyStr = privateKeyInput.value.trim();
          
          if (!privateKeyStr) {
            showError('private-key-error', '请输入私钥');
            return;
          }
          
          wallet = generateWalletFromPrivateKey(privateKeyStr);
        }

        currentWallet = wallet;
        
        // 生成 Labitbu 图像
        // 注意：Xverse 返回的是带前缀的压缩公钥，然后 slice(2) 去掉前缀
        // 我们需要模拟相同的行为
        const keyPair = ECPair.fromPrivateKey(wallet.privateKey, { compressed: true });
        const compressedPubkey = keyPair.publicKey.toString('hex');
        const pubkeyHex = compressedPubkey.slice(2); // 去掉压缩标志，与 Xverse 行为一致
        
        console.log('生成的公钥 (去掉前缀):', pubkeyHex);
        console.log('完整压缩公钥:', compressedPubkey);
        
        currentLabitbuBytes = generate_labitbu_bytes(pubkeyHex, sleepyBase, sleepyAcc);

        // 创建存款地址
        const addressBytes = create_deposit_address(pubkeyHex, Array.from(currentLabitbuBytes));
        currentDepositAddress = new TextDecoder().decode(addressBytes);

        // 显示钱包信息
        const derivationPathValue = document.getElementById('derivation-path').value;
        document.getElementById('derivation-path-display').textContent = derivationPathValue;
        document.getElementById('wallet-pubkey').textContent = pubkeyHex;
        document.getElementById('wallet-address').textContent = wallet.address;
        document.getElementById('labitbu-address').textContent = currentDepositAddress;

        // 渲染 Labitbu 图像
        const url = URL.createObjectURL(new Blob([currentLabitbuBytes], { type: 'image/webp' }));
        const img = new Image();
        img.src = url;
        img.style.maxWidth = '200px';
        img.style.borderRadius = '10px';
        document.getElementById('labitbu-preview').replaceChildren(img);

        // 显示相关部分
        walletInfo.style.display = 'block';
        singleMintSection.style.display = 'block';
        batchMintSection.style.display = 'block';

      } catch (error) {
        const activeTab = document.querySelector('.tab-content.active');
        const errorId = activeTab.id === 'tab-content-mnemonic' ? 'mnemonic-error' : 'private-key-error';
        showError(errorId, error.message);
      }
    };

    // 清除输入
    clearInputsBtn.onclick = () => {
      if (confirm('确定要清除所有输入和历史记录吗？')) {
        mnemonicInput.value = '';
        privateKeyInput.value = '';
        destinationAddress.value = '';
        document.getElementById('batch-addresses').value = '';
        clearErrors();
        
        // 隐藏钱包信息
        walletInfo.style.display = 'none';
        singleMintSection.style.display = 'none';
        batchMintSection.style.display = 'none';
        
        // 清除状态
        currentWallet = null;
        currentLabitbuBytes = null;
        currentDepositAddress = null;
        
        // 清除交易历史
        clearTransactionHistory();
        
        // 清除单次铸造状态显示
        document.getElementById('single-mint-status').innerHTML = '';
        document.getElementById('batch-results').innerHTML = '';
      }
    };

    // 检查余额
    checkBalanceBtn.onclick = async () => {
      if (!currentDepositAddress) return;
      
      try {
        let utxos;
        if (testMode) {
          // 测试模式：使用虚拟UTXO
          utxos = generateMockUTXOs(currentDepositAddress);
          console.log('🧪 测试模式：使用虚拟余额查询', utxos);
        } else {
          // 正常模式：查询真实UTXO
          utxos = await addresses.getAddressTxsUtxo({ address: currentDepositAddress });
        }
        
        const balance = utxos.reduce((sum, utxo) => sum + Number(utxo.value), 0);
        
        if (testMode) {
          showSuccess('single-mint-status', `🧪 测试模式 - 发现 ${utxos.length} 个虚拟UTXO，总余额: ${balance} sats`);
        } else {
          showSuccess('single-mint-status', `发现 ${utxos.length} 个 UTXO，总余额: ${balance} sats`);
        }
        
        if (utxos.length > 0 && destinationAddress.value.trim()) {
          singleMintBtn.disabled = false;
        }
      } catch (error) {
        showError('single-mint-status', `检查余额失败: ${error.message}`);
      }
    };

    // 地址验证
    destinationAddress.addEventListener('input', () => {
      const addr = destinationAddress.value.trim();
      const errorEl = document.getElementById('address-error');
      
      if (!addr) {
        errorEl.textContent = '';
        return;
      }
      
      if (!addr.startsWith('bc1p')) {
        errorEl.textContent = '必须以 bc1p 开头';
        return;
      }
      
      if (addr.length !== 62) {
        errorEl.textContent = '必须是 62 个字符';
        return;
      }
      
      errorEl.textContent = '';
    });

    // 单次铸造
    singleMintBtn.onclick = async () => {
      if (!currentWallet || !currentLabitbuBytes || !currentDepositAddress) return;
      
      const destAddr = destinationAddress.value.trim();
      if (!destAddr || !destAddr.startsWith('bc1p') || destAddr.length !== 62) {
        showError('address-error', '请输入有效的目标地址');
        return;
      }

      try {
        singleMintBtn.disabled = true;
        showSuccess('single-mint-status', '正在构建交易...');

        let utxos;
        if (testMode) {
          // 测试模式：使用虚拟UTXO
          utxos = generateMockUTXOs(currentDepositAddress);
          console.log('🧪 测试模式：使用虚拟UTXO', utxos);
          console.log('🔍 UTXO 类型检查:', utxos.map(u => ({ value: u.value, type: typeof u.value })));
        } else {
          // 正常模式：从mempool获取真实UTXO
          utxos = await addresses.getAddressTxsUtxo({ address: currentDepositAddress });
          if (utxos.length === 0) {
            throw new Error('没有找到可用的 UTXO');
          }
        }

        // 使用自定义费率 (支持小数)
        const feeRateNum = parseFloat(customFeeRate) || 20;
        
        // 智能选择 UTXO：选择一个足够支付费用的最小 UTXO
        console.log('🔍 UTXO选择调试 - 原始UTXOS:', utxos.map(u => ({ value: u.value, type: typeof u.value })));
        
        utxos.sort((a, b) => a.value - b.value); // 按金额排序
        let selectedUtxos = [];
        let total = 0;
        
        for (const utxo of utxos) {
          selectedUtxos.push(utxo);
          const utxoValue = utxo.value;
          console.log(`🔍 添加UTXO: value=${utxoValue} (${typeof utxoValue})`);
          
          total += utxoValue;
          console.log(`🔍 当前总计: ${total} (${typeof total})`);
          
          // 使用实际测试的交易大小 1150 字节（包含 Labitbu 图片数据）
          const actualFee = Math.ceil(feeRateNum * 1150);
          
          if (total >= actualFee + 330) { // 330是最小输出金额
            console.log(`🔍 UTXO选择完成: total=${total}, actualFee=${actualFee}`);
            break;
          }
        }
        
        // 更新为选中的UTXO
        utxos = selectedUtxos;
        const finalFee = Math.ceil(feeRateNum * 1150); // 使用实际测试的大小
        
        // 基于选中的UTXO构建交易输入
        console.log('🔍 构建交易输入 - 选中的UTXO:', utxos.map(u => ({ txid: u.txid, vout: u.vout, value: u.value, type: typeof u.value })));
        
        const inputs = utxos.map(u => ({
          previous_output: `${u.txid}:${u.vout}`,
          sequence: 0xFFFFFFFD,
          script_sig: '',
          witness: []
        }));
        
        console.log('🔍 构建的inputs:', inputs);

        // 基于选中的UTXO获取前一笔交易输出
        let prevOuts;
        if (testMode) {
          // 测试模式：生成虚拟前一笔交易输出
          prevOuts = utxos.map(u => {
            const prevOut = {
              value: u.value,
              script_pubkey: '51201234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef' // 虚假的script
            };
            console.log(`🔍 测试模式 prevOut: value=${prevOut.value} (${typeof prevOut.value})`);
            return prevOut;
          });
          console.log('🧪 测试模式：使用虚拟 prevOuts', prevOuts);
        } else {
          // 正常模式：从mempool获取真实交易输出
          prevOuts = await Promise.all(utxos.map(async ({ txid, vout }) => {
            const tx = await transactions.getTx({ txid });
            const out = tx.vout[vout];
            const prevOut = {
              value: Number(out.value),
              script_pubkey: out.scriptpubkey
            };
            console.log(`🔍 正常模式 prevOut: value=${prevOut.value} (${typeof prevOut.value})`);
            return prevOut;
          }));
        }
        
        console.log(`🔧 选择了 ${utxos.length} 个UTXO，总计: ${total} sats，费用: ${finalFee} sats`);

        if (finalFee >= total) {
          throw new Error('手续费大于等于余额');
        }

        if (total - finalFee < 330) {
          throw new Error('输出金额过小（粉尘）');
        }

        // 创建 PSBT
        const pubkeyHex = Buffer.from(currentWallet.publicKey).toString('hex');
        
        // 转换数据为正确格式
        const toJSValue = (obj) => obj;
        
        // 详细调试 mint 函数参数
        console.log('🔍 mint 函数参数调试:');
        console.log('  pubkeyHex:', pubkeyHex, typeof pubkeyHex);
        console.log('  currentLabitbuBytes:', Array.from(currentLabitbuBytes), typeof currentLabitbuBytes);
        console.log('  total:', total, typeof total);
        console.log('  destAddr:', destAddr, typeof destAddr);
        console.log('  finalFee:', finalFee, typeof finalFee);
        console.log('  inputs:', toJSValue(inputs));
        console.log('  prevOuts:', toJSValue(prevOuts));
        
        let psbtBytes;
        try {
          // mint 函数需要 bigint 类型的 amount 和 fee
          psbtBytes = mint(
            pubkeyHex,
            Array.from(currentLabitbuBytes),
            BigInt(total),      // 转换为 bigint
            destAddr,
            BigInt(finalFee),   // 转换为 bigint
            toJSValue(inputs),
            toJSValue(prevOuts)
          );
          console.log('✅ mint 函数调用成功');
        } catch (error) {
          console.error('❌ mint 函数调用失败:', error);
          throw error;
        }

        // 签名交易
        const psbt = bitcoin.Psbt.fromBuffer(Buffer.from(psbtBytes));
        
        // 为每个输入签名
        for (let i = 0; i < inputs.length; i++) {
          psbt.signInput(i, ECPair.fromPrivateKey(currentWallet.privateKey));
        }

        psbt.finalizeAllInputs();
        const signedTx = psbt.extractTransaction();
        const txHex = signedTx.toHex();
        const txId = signedTx.getId();
        
        if (testMode) {
          // 测试模式：不广播交易
          showSuccess('single-mint-status', `🧪 测试模式 - 交易构建成功但未广播。交易 ID: ${txId}`);
          console.log('🧪 测试模式交易十六进制:', txHex);
        } else {
          // 正常模式：广播交易
          showSuccess('single-mint-status', '正在广播交易...');
          try {
            await broadcastTransaction(txHex);
            showSuccess('single-mint-status', `✅ 交易广播成功！交易 ID: ${txId}`);
          } catch (error) {
            console.error('交易广播失败:', error);
            showError('single-mint-status', `❌ 交易广播失败: ${error.message}`);
            showSuccess('single-mint-status', `交易已构建完成但广播失败。交易 ID: ${txId}`);
          }
        }
        
        // 准备交易数据用于RBF提速（测试模式也保存，用于提速测试）
        const transactionData = {
          pubkeyHex: pubkeyHex,
          labitbuBytes: currentLabitbuBytes,
          total: total,
          originalFee: finalFee,
          inputs: inputs,
          prevOuts: prevOuts,
          wallet: currentWallet
        };

        // 添加到交易历史（包含交易Hex）
        addToTransactionHistory(1, txId, destAddr, total - finalFee, transactionData, txHex);
        
        // 显示交易十六进制供手动广播
        const txDetails = document.createElement('div');
        txDetails.innerHTML = `
          <h4>交易详情</h4>
          <p><strong>交易 ID:</strong> ${signedTx.getId()}</p>
          <p><strong>交易十六进制:</strong></p>
          <textarea readonly style="width:100%;height:100px;font-family:monospace">${txHex}</textarea>
          <p><a href="https://mempool.space/tx/${signedTx.getId()}" target="_blank">在区块浏览器中查看</a></p>
        `;
        
        // 临时显示交易详情
        const tempDiv = document.createElement('div');
        tempDiv.appendChild(txDetails);
        document.getElementById('single-mint-status').appendChild(tempDiv);

      } catch (error) {
        showError('single-mint-status', `铸造失败: ${error.message}`);
      } finally {
        singleMintBtn.disabled = false;
      }
    };

    // 批量铸造相关功能
    let derivedAddressesData = []; // 存储派生地址数据
    
    // 批量铸造UI元素
    const batchProgress = document.getElementById('batch-progress');
    const progressFill = document.getElementById('progress-fill');
    const batchStatus = document.getElementById('batch-status');
    const batchResults = document.getElementById('batch-results');
    

    
    // 生成多个派生地址
    function generateMultipleAddresses(mnemonic, basePath, count) {
      const addresses = [];
      const seed = bip39.mnemonicToSeedSync(mnemonic);
      const root = bip32.fromSeed(seed);
      
      console.log(`🔄 开始生成 ${count} 个派生地址...`);
      
      for (let i = 0; i < count; i++) {
        try {
          // 处理派生路径：
          // 所有地址都基于basePath，只修改最后一个地址索引
          // 例如：basePath是 m/84'/0'/0'/0/0，那么地址i就是 m/84'/0'/0'/0/i
          const pathParts = basePath.split('/');
          pathParts[pathParts.length - 1] = i.toString(); // 修改最后一个索引为 i
          const fullPath = pathParts.join('/');
          
          console.log(`🔍 地址 ${i} 派生路径: ${fullPath} (basePath: ${basePath})`);
          const child = root.derivePath(fullPath);
          const privateKey = child.privateKey;
          const keyPair = ECPair.fromPrivateKey(privateKey, { compressed: true });
          const publicKey = keyPair.publicKey;
          
          // 根据派生路径类型生成对应的地址
          let address, addressType;
          if (basePath.startsWith("m/44'")) {
            // P2PKH
            const { address: p2pkhAddr } = bitcoin.payments.p2pkh({ pubkey: publicKey });
            address = p2pkhAddr;
            addressType = 'P2PKH';
          } else if (basePath.startsWith("m/84'")) {
            // P2WPKH
            const { address: p2wpkhAddr } = bitcoin.payments.p2wpkh({ pubkey: publicKey });
            address = p2wpkhAddr;
            addressType = 'P2WPKH';
          } else if (basePath.startsWith("m/86'")) {
            // P2TR
            const { address: p2trAddr } = bitcoin.payments.p2tr({ 
              internalPubkey: publicKey.slice(1, 33) 
            });
            address = p2trAddr;
            addressType = 'P2TR';
          }
          
          // 生成 Labitbu 存款地址  
          const compressedPubkey = publicKey.toString('hex');
          const pubkeyHex = compressedPubkey.slice(2); // 去掉压缩标志，与 Xverse 行为一致
          
          console.log(`🔍 地址 ${i} 公钥处理:`);
          console.log('  完整压缩公钥:', compressedPubkey, '长度:', compressedPubkey.length);
          console.log('  去掉前缀后:', pubkeyHex, '长度:', pubkeyHex.length);
          const labitbuBytes = generate_labitbu_bytes(pubkeyHex, sleepyBase, sleepyAcc);
          const addressBytes = create_deposit_address(pubkeyHex, Array.from(labitbuBytes));
          const labitbuAddress = new TextDecoder().decode(addressBytes);
          
          addresses.push({
            index: i,
            path: fullPath,
            addressType: addressType,
            wallet: {
              privateKey: privateKey,
              publicKey: publicKey,
              address: address,
              labitbuAddress: labitbuAddress,
              pubkey: pubkeyHex
            },
            utxos: [],
            balance: 0,
            canMint: false
          });
          
          console.log(`✅ 生成地址 ${i}: ${address} (${addressType})`);
        } catch (error) {
          console.error(`❌ 生成地址 ${i} 失败:`, error);
        }
      }
      
      console.log(`🎉 成功生成 ${addresses.length} 个派生地址`);
      return addresses;
    }
    
    // 扫描派生地址余额
    window.scanDerivedAddresses = async function() {
      const mnemonicInput = document.getElementById('mnemonic-input').value.trim();
      if (!mnemonicInput) {
        alert('请先导入助记词');
        return;
      }
      
      if (!currentWallet) {
        alert('请先生成钱包信息');
        return;
      }
      
      const derivationCount = parseInt(document.getElementById('derivation-count').value);
      if (derivationCount < 1 || derivationCount > 500) {
        alert('派生地址数量必须在1-500之间');
        return;
      }
      
      const statusDiv = document.getElementById('derived-addresses-status');
      statusDiv.innerHTML = '<div style="color: #007bff;">⏳ 正在生成派生地址...</div>';
      
      try {
        // 获取当前派生路径
        const currentPath = document.getElementById('derivation-path').value || 
                           document.getElementById('derivation-path-input').value;
        
        // 生成多个派生地址
        derivedAddressesData = generateMultipleAddresses(
          document.getElementById('mnemonic-input').value,
          currentPath,
          derivationCount
        );
        
        statusDiv.innerHTML = '<div style="color: #007bff;">📡 正在查询余额...</div>';
        
        // 批量查询余额
        let successCount = 0;
        for (let i = 0; i < derivedAddressesData.length; i++) {
          const addr = derivedAddressesData[i];
          try {
            if (testMode) {
              // 测试模式：生成虚拟UTXO
              addr.utxos = generateMockUTXOs(addr.wallet.labitbuAddress);
              addr.balance = addr.utxos.reduce((sum, utxo) => sum + utxo.value, 0);
            } else {
              // 真实模式：查询Mempool
              addr.utxos = await addresses.getAddressTxsUtxo({ address: addr.wallet.labitbuAddress });
              addr.balance = addr.utxos.reduce((sum, utxo) => sum + utxo.value, 0);
            }
            addr.canMint = addr.balance >= 600; // 最低余额要求
            successCount++;
          } catch (error) {
            console.error(`查询地址 ${i} 余额失败:`, error);
            addr.balance = 0;
            addr.canMint = false;
          }
        }
        
        // 显示结果
        displayDerivedAddressesStatus();
        
        // 启用批量铸造按钮
        const canMintCount = derivedAddressesData.filter(addr => addr.canMint).length;
        document.getElementById('start-multi-batch-btn').disabled = canMintCount === 0;
        
        console.log(`📊 扫描完成: ${successCount}/${derivationCount} 成功, ${canMintCount} 个可铸造`);
        
      } catch (error) {
        console.error('扫描派生地址失败:', error);
        statusDiv.innerHTML = `<div style="color: red;">❌ 扫描失败: ${error.message}</div>`;
      }
    };
    
    // 显示派生地址状态
    function displayDerivedAddressesStatus() {
      const statusDiv = document.getElementById('derived-addresses-status');
      if (derivedAddressesData.length === 0) {
        statusDiv.innerHTML = '';
        return;
      }
      
      const canMintCount = derivedAddressesData.filter(addr => addr.canMint).length;
      let html = `
        <div style="margin: 10px 0; padding: 10px; background: #e7f3ff; border-radius: 5px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h5 style="margin: 0;">📊 派生地址扫描结果 (${canMintCount}/${derivedAddressesData.length} 可铸造)</h5>
            <button onclick="copyBatchTransferData()" style="background: #28a745; color: white; padding: 5px 10px; border: none; border-radius: 3px; font-size: 12px; cursor: pointer;" title="复制批量转账数据">
              📋 复制转账数据
            </button>
          </div>
          <div style="max-height: 300px; overflow-y: auto;">
      `;
      
      derivedAddressesData.forEach(addr => {
        const statusColor = addr.canMint ? '#d4edda' : '#f8d7da';
        const statusIcon = addr.canMint ? '✅' : '❌';
        const statusText = addr.canMint ? '可铸造' : '余额不足';
        
        html += `
          <div style="padding: 8px; border: 1px solid #ddd; margin: 3px 0; border-radius: 3px; background: ${statusColor};">
            <div><strong>地址 ${addr.index}</strong> (${addr.addressType}): 
              <code style="font-size: 11px;">${addr.wallet.address}</code>
            </div>
            <div style="font-size: 12px;">
              <strong>Labitbu存款地址:</strong> <code style="font-size: 11px;">${addr.wallet.labitbuAddress}</code>
            </div>
            <div style="font-size: 12px;">
              余额: ${addr.balance.toLocaleString()} sats ${statusIcon} ${statusText}
            </div>
            <div style="font-size: 11px; color: #666;">
              派生路径: ${addr.path} | 公钥: ${addr.wallet.pubkey}
            </div>
          </div>
        `;
      });
      
      html += '</div></div>';
      statusDiv.innerHTML = html;
    }
    
    // 复制批量转账数据
    window.copyBatchTransferData = function() {
      if (derivedAddressesData.length === 0) {
        alert('请先扫描派生地址');
        return;
      }
      
      // 获取当前费率
      const feeRateNum = parseFloat(document.getElementById('custom-fee-rate').value) || 20;
      
      // 计算每个地址所需的最低金额
      // 费用 = 费率 * 1150 字节
      // 最低金额 = 费用 + dust limit (330 sats)
      const feePerAddress = Math.ceil(feeRateNum * 1150);
      const dustLimit = 330;
      const minAmountPerAddress = feePerAddress + dustLimit;
      
      // 生成转账数据格式: 地址,金额
      const transferData = derivedAddressesData.map(addr => {
        return `${addr.wallet.labitbuAddress},${minAmountPerAddress}`;
      }).join('\n');
      
      console.log('📋 生成的批量转账数据:');
      console.log(transferData);
      console.log(`💰 每个地址最低金额: ${minAmountPerAddress} sats (费用: ${feePerAddress} + dust: ${dustLimit})`);
      
      // 复制到剪贴板
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(transferData).then(() => {
          // 临时改变按钮文字
          const button = event?.target;
          if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '✅ 已复制';
            button.style.background = '#28a745';
            setTimeout(() => {
              button.innerHTML = originalText;
              button.style.background = '#28a745';
            }, 2000);
          }
          
          // 显示详细信息
          alert(`✅ 批量转账数据已复制到剪贴板！\n\n📊 统计信息:\n• 地址数量: ${derivedAddressesData.length}\n• 费率: ${feeRateNum} sat/vB\n• 每个地址费用: ${feePerAddress} sats\n• 每个地址最低金额: ${minAmountPerAddress} sats\n• 总金额需求: ${(minAmountPerAddress * derivedAddressesData.length).toLocaleString()} sats`);
        }).catch(err => {
          console.error('复制失败:', err);
          fallbackCopyTextToClipboard(transferData);
        });
      } else {
        fallbackCopyTextToClipboard(transferData);
      }
    };

    // 广播交易函数
    async function broadcastTransaction(txHex) {
      // 使用多个广播服务提高成功率
      const broadcastServices = [
        {
          name: 'Mempool.space',
          url: 'https://mempool.space/api/tx',
          method: 'POST',
          headers: { 'Content-Type': 'text/plain' },
          body: txHex
        },
        {
          name: 'Blockstream',
          url: 'https://blockstream.info/api/tx',
          method: 'POST', 
          headers: { 'Content-Type': 'text/plain' },
          body: txHex
        }
      ];
      
      let lastError = null;
      
      for (const service of broadcastServices) {
        try {
          console.log(`📡 尝试通过 ${service.name} 广播交易...`);
          
          const response = await fetch(service.url, {
            method: service.method,
            headers: service.headers,
            body: service.body
          });
          
          if (response.ok) {
            const result = await response.text();
            console.log(`✅ 通过 ${service.name} 广播成功:`, result);
            return result;
          } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
        } catch (error) {
          console.warn(`❌ ${service.name} 广播失败:`, error.message);
          lastError = error;
          continue;
        }
      }
      
      // 所有服务都失败了
      throw new Error(`所有广播服务都失败了。最后错误: ${lastError?.message || '未知错误'}`);
    }

    // 多地址批量铸造按钮事件
    document.getElementById('start-multi-batch-btn').onclick = async () => {
      await startMultiAddressBatchMint();
    };
    
    // 多地址批量铸造主函数
    async function startMultiAddressBatchMint() {
      if (derivedAddressesData.length === 0) {
        alert('请先扫描派生地址');
        return;
      }
      
      const canMintAddresses = derivedAddressesData.filter(addr => addr.canMint);
      if (canMintAddresses.length === 0) {
        alert('没有可铸造的地址');
        return;
      }
      
      // 获取目标地址列表
      const batchAddresses = document.getElementById('multi-batch-addresses').value
        .split('\n')
        .map(addr => addr.trim())
        .filter(addr => addr);
      
      const defaultDestAddr = document.getElementById('destination-address').value;
      
      // 验证目标地址设置
      if (batchAddresses.length === 0 && !defaultDestAddr) {
        alert('请设置目标地址');
        return;
      }
      
      // 如果批量地址数量不匹配，给出提示
      if (batchAddresses.length > 0 && batchAddresses.length !== canMintAddresses.length) {
        const confirmMessage = `派生地址数量: ${canMintAddresses.length}\n批量目标地址数量: ${batchAddresses.length}\n\n地址数量不匹配！\n\n选择处理方式:\n• 确定: 使用默认目标地址\n• 取消: 重新设置批量目标地址`;
        if (!confirm(confirmMessage)) {
          return;
        }
        // 用户选择使用默认地址，清空批量地址
        batchAddresses.length = 0;
      }
      
      console.log(`🚀 开始多地址批量铸造: ${canMintAddresses.length} 个地址`);
      
      // 显示进度
      batchProgress.style.display = 'block';
      batchResults.innerHTML = '';
      
      let successCount = 0;
      let failCount = 0;
      
      for (let i = 0; i < canMintAddresses.length; i++) {
        const addr = canMintAddresses[i];
        
        // 更新进度
        const progress = ((i + 1) / canMintAddresses.length) * 100;
        progressFill.style.width = progress + '%';
        batchStatus.textContent = `正在铸造地址 ${addr.index + 1}/${canMintAddresses.length}...`;
        
        try {
          // 确定当前地址的目标地址
          let currentDestAddr;
          if (batchAddresses.length > 0) {
            // 使用对应的批量目标地址
            currentDestAddr = batchAddresses[i];
          } else {
            // 使用默认目标地址
            currentDestAddr = defaultDestAddr;
          }
          
          console.log(`📍 地址 ${addr.index} → 目标地址: ${currentDestAddr}`);
          
          // 为每个地址执行铸造
          const result = await mintSingleAddressLabitbu(addr, currentDestAddr, i + 1);
          
          if (result.success) {
            successCount++;
            
            // 添加到交易历史
            addToTransactionHistory(
              `Multi-${addr.index}`, 
              result.txId, 
              currentDestAddr, 
              result.amount,
              result.transactionData,
              result.txHex
            );
            
            // 显示成功结果
            const resultItem = document.createElement('div');
            const broadcastText = testMode ? ' (测试模式 - 未广播)' : ' (已广播)';
            resultItem.innerHTML = `
              <div style="padding: 10px; border: 1px solid #d4edda; background: #d4edda; margin: 5px 0; border-radius: 3px;">
                <strong>✅ 地址 ${addr.index} 铸造成功${broadcastText}</strong><br>
                <small>交易ID: ${result.txId}</small><br>
                <small>金额: ${result.amount} sats</small>
              </div>
            `;
            batchResults.appendChild(resultItem);
            
          } else {
            throw new Error(result.error);
          }
          
        } catch (error) {
          failCount++;
          console.error(`地址 ${addr.index} 铸造失败:`, error);
          
          // 显示失败结果
          const resultItem = document.createElement('div');
          resultItem.innerHTML = `
            <div style="padding: 10px; border: 1px solid #f8d7da; background: #f8d7da; margin: 5px 0; border-radius: 3px;">
              <strong>❌ 地址 ${addr.index} 铸造失败</strong><br>
              <small>错误: ${error.message}</small>
            </div>
          `;
          batchResults.appendChild(resultItem);
        }
        
        // 延迟避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // 完成
      batchStatus.textContent = `多地址批量铸造完成！成功: ${successCount}, 失败: ${failCount}`;
      console.log(`🎉 多地址批量铸造完成: 成功 ${successCount}, 失败 ${failCount}`);
    }
    
    // 单个地址铸造函数
    async function mintSingleAddressLabitbu(addressData, destAddr, labitbuNumber) {
      try {
        console.log(`🔄 开始铸造地址 ${addressData.index} 的 Labitbu...`);
        
        // 生成当前地址的 Labitbu 图像数据
        const labitbuBytes = generate_labitbu_bytes(addressData.wallet.pubkey, sleepyBase, sleepyAcc);
        
        // 智能选择UTXO
        const sortedUtxos = addressData.utxos.sort((a, b) => b.value - a.value);
        const feeRateNum = parseFloat(document.getElementById('custom-fee-rate').value);
        let selectedUtxos = [];
        let total = 0;
        
        // 计算所需总金额 (输出 + 手续费)
        const finalFee = Math.ceil(feeRateNum * 1150);
        const requiredAmount = finalFee + 330; // dust limit
        
        for (const utxo of sortedUtxos) {
          selectedUtxos.push(utxo);
          total += utxo.value;
          if (total >= requiredAmount) break;
        }
        
        if (total < requiredAmount) {
          throw new Error('余额不足以支付交易费用');
        }
        
        console.log(`💰 选择了 ${selectedUtxos.length} 个UTXO, 总额: ${total} sats, 费用: ${finalFee} sats`);
        
        // 构建交易输入 (与单个铸造保持一致的格式)
        const inputs = selectedUtxos.map(utxo => ({
          previous_output: `${utxo.txid}:${utxo.vout}`,
          sequence: 0xFFFFFFFD,
          script_sig: '',
          witness: []
        }));
        
        // 构建前一笔交易输出 (与单个铸造保持一致的格式)
        let prevOuts;
        if (testMode) {
          // 测试模式：使用虚拟脚本
          prevOuts = selectedUtxos.map(utxo => ({
            value: utxo.value,
            script_pubkey: utxo.scriptPubKey || '51201234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef'
          }));
        } else {
          // 正常模式：从mempool获取真实交易输出
          prevOuts = await Promise.all(selectedUtxos.map(async ({ txid, vout, value }) => {
            const tx = await transactions.getTx({ txid });
            const out = tx.vout[vout];
            return {
              value: Number(out.value),
              script_pubkey: out.scriptpubkey
            };
          }));
        }
        
        console.log(`🔍 调用WASM mint函数: 金额=${total}, 费用=${finalFee}`);
        
        // 调用WASM函数
        const toJSValue = (obj) => obj;
        const psbtBytes = mint(
          addressData.wallet.pubkey,
          Array.from(labitbuBytes),
          BigInt(total),
          destAddr,
          BigInt(finalFee),
          toJSValue(inputs),
          toJSValue(prevOuts)
        );
        
        console.log(`📦 生成PSBT成功, 字节长度: ${psbtBytes.length}`);
        
        // 签名交易
        const psbt = bitcoin.Psbt.fromBuffer(Buffer.from(psbtBytes));
        
        // 为每个输入签名
        for (let i = 0; i < inputs.length; i++) {
          const keyPair = ECPair.fromPrivateKey(addressData.wallet.privateKey, { compressed: true });
          psbt.signInput(i, keyPair);
        }
        
        psbt.finalizeAllInputs();
        const signedTx = psbt.extractTransaction();
        const signedTxHex = signedTx.toHex();
        const signedTxId = signedTx.getId();
        
        console.log(`✅ 地址 ${addressData.index} 交易签名完成: ${signedTxId}`);
        
        // 广播交易 (仅在正常模式下)
        let broadcastSuccess = false;
        if (!testMode) {
          try {
            console.log(`📡 正在广播地址 ${addressData.index} 的交易...`);
            await broadcastTransaction(signedTxHex);
            broadcastSuccess = true;
            console.log(`✅ 地址 ${addressData.index} 交易广播成功: ${signedTxId}`);
          } catch (error) {
            console.error(`❌ 地址 ${addressData.index} 交易广播失败:`, error);
            // 广播失败不影响返回结果，用户可以手动广播
          }
        }
        
        // 返回结果
        return {
          success: true,
          txId: signedTxId,
          txHex: signedTxHex,
          amount: total - finalFee,
          transactionData: {
            pubkeyHex: addressData.wallet.pubkey,
            labitbuBytes: labitbuBytes,
            total: total,
            originalFee: finalFee,
            inputs: inputs,
            prevOuts: prevOuts,
            wallet: addressData.wallet,
            destAddr: destAddr
          }
        };
        
      } catch (error) {
        console.error(`❌ 地址 ${addressData.index} 铸造失败:`, error);
        return {
          success: false,
          error: error.message
        };
      }
    }

    // 交易历史管理
    let transactionHistory = [];
    
    // 从 localStorage 加载交易历史
    function loadTransactionHistory() {
      try {
        const saved = loadFromStorage(STORAGE_KEYS.TRANSACTION_HISTORY);
        if (saved) {
          transactionHistory = JSON.parse(saved).map(item => ({
            ...item,
            timestamp: new Date(item.timestamp) // 恢复 Date 对象
          }));
          updateTransactionHistoryDisplay();
        }
      } catch (error) {
        console.warn('加载交易历史失败:', error);
        transactionHistory = [];
      }
    }

    // 保存交易历史到 localStorage
    function saveTransactionHistory() {
      try {
        saveToStorage(STORAGE_KEYS.TRANSACTION_HISTORY, JSON.stringify(transactionHistory));
      } catch (error) {
        console.warn('保存交易历史失败:', error);
      }
    }
    
    function addToTransactionHistory(labitbuNumber, txId, targetAddress, amount, transactionData = null, txHex = null) {
      const historyItem = {
        id: Date.now(),
        timestamp: new Date(),
        labitbuNumber,
        txId,
        targetAddress,
        amount,
        testMode: testMode,
        txHex: txHex, // 保存交易十六进制用于复制
        isSpeedUp: false, // 标记是否为提速交易
        originalTxId: null, // 如果是提速交易，记录原交易ID
        speedUpHistory: [], // 记录提速历史
        // 保存交易构建信息用于RBF提速（测试模式也保存，用于提速测试）
        transactionData: transactionData ? {
          pubkeyHex: transactionData.pubkeyHex,
          labitbuBytes: Array.from(transactionData.labitbuBytes), // 转换为普通数组便于保存
          total: transactionData.total,
          originalFee: transactionData.originalFee,
          inputs: transactionData.inputs,
          prevOuts: transactionData.prevOuts,
          wallet: {
            privateKey: Array.from(transactionData.wallet.privateKey), // 转换为普通数组
            publicKey: Array.from(transactionData.wallet.publicKey)
          },
          destAddr: targetAddress // 保存目标地址
        } : null
      };
      transactionHistory.unshift(historyItem); // 新的放在前面
      saveTransactionHistory(); // 保存到 localStorage
      updateTransactionHistoryDisplay();
    }
    
    function updateTransactionHistoryDisplay() {
      const historyDiv = document.getElementById('transaction-history');
      if (transactionHistory.length === 0) {
        historyDiv.innerHTML = '';
        return;
      }
      
      let historyHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h3 style="margin: 0;">交易历史 (${transactionHistory.length})</h3>
          <div>
            <button onclick="batchSpeedUpAllTransactions()" style="background: #ff6b35; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-right: 5px;">
              ⚡ 一键提速全部
            </button>
            <button onclick="clearAllTransactionHistory()" style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-right: 5px;">
              🗑️ 清空全部
            </button>
          </div>
        </div>
        <div style="max-height: 400px; overflow-y: auto;">
      `;
      
      transactionHistory.forEach(item => {
        const modeIcon = item.testMode ? '🧪' : '⛓️';
        const modeText = item.testMode ? '测试模式' : '正常模式';
        const canSpeedUp = item.transactionData && !item.isSpeedUp; // 有交易数据且不是提速交易才能提速
        
        // 构建提速历史显示
        let speedUpHistoryHtml = '';
        if (item.speedUpHistory && item.speedUpHistory.length > 0) {
          speedUpHistoryHtml = `
            <div style="margin-top: 8px; padding: 8px; background: #fff3cd; border-radius: 4px; border-left: 3px solid #ffc107;">
              <div style="font-size: 12px; font-weight: bold; color: #856404; margin-bottom: 4px;">⚡ 提速历史:</div>
              ${item.speedUpHistory.map(speedUp => `
                <div style="font-size: 11px; color: #856404;">
                  ${speedUp.timestamp.toLocaleString()} - ${speedUp.oldFeeRate} → ${speedUp.newFeeRate} sat/vB
                  ${speedUp.newTxId ? `(新交易: ${speedUp.newTxId.substring(0, 16)}...)` : ''}
                </div>
              `).join('')}
            </div>
          `;
        }
        
        historyHTML += `
          <div style="border: 1px solid #dee2e6; padding: 10px; margin: 5px 0; border-radius: 5px; background: #f8f9fa; position: relative;">
            <button onclick="deleteTransactionHistory('${item.id}')" style="position: absolute; top: 5px; right: 30px; background: #dc3545; color: white; border: none; width: 20px; height: 20px; border-radius: 50%; font-size: 12px; cursor: pointer; display: flex; align-items: center; justify-content: center;" title="删除此记录">×</button>
            
            <div><strong>${modeIcon} Labitbu #${item.labitbuNumber}${item.isSpeedUp ? ' (提速)' : ''}</strong> - ${item.timestamp.toLocaleString()}</div>
            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">${modeText}</div>
            <div>目标地址: <code style="font-size: 11px;">${item.targetAddress}</code></div>
            <div>输出金额: ${item.amount} sats</div>
            <div>交易ID: ${item.testMode ? 
              `<code style="font-size: 11px;">${item.txId}</code> <span style="color: #666;">(未广播)</span>` : 
              `<a href="https://mempool.space/tx/${item.txId}" target="_blank" style="font-size: 11px;">${item.txId}</a>`
            }</div>
            
            <div style="margin-top: 8px; display: flex; gap: 5px; flex-wrap: wrap;">
              ${item.txHex ? `
                <button onclick="copyTransactionHex('${item.id}', this)" style="background: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px; cursor: pointer;" title="复制交易十六进制">
                  📋 复制Hex
                </button>
              ` : ''}
              
              ${canSpeedUp ? `
                <button onclick="speedUpTransaction('${item.id}')" style="background: #ff6b35; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px; cursor: pointer;">
                  ⚡ ${item.testMode ? '提速测试' : 'RBF 提速'}
                </button>
                <span style="font-size: 12px; color: #666; align-self: center;">
                  费率: ${(item.transactionData.originalFee / 1150).toFixed(1)} sat/vB
                </span>
              ` : ''}
            </div>
            
            ${speedUpHistoryHtml}
          </div>
        `;
      });
      historyHTML += '</div>';
      historyDiv.innerHTML = historyHTML;
    }
    
    // 复制交易Hex功能
    window.copyTransactionHex = function(transactionId, buttonElement) {
      const historyItem = transactionHistory.find(item => item.id == transactionId);
      if (!historyItem || !historyItem.txHex) {
        alert('找不到交易十六进制数据');
        return;
      }

      // 如果没有传入按钮元素，尝试通过事件获取
      const button = buttonElement || event?.target;

      // 使用现代的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(historyItem.txHex).then(() => {
          // 临时改变按钮文字提供反馈
          if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '✅ 已复制';
            button.style.background = '#28a745';
            setTimeout(() => {
              button.innerHTML = originalText;
              button.style.background = '#6c757d';
            }, 2000);
          } else {
            console.log('✅ 交易十六进制已复制到剪贴板');
          }
        }).catch(err => {
          console.error('复制失败:', err);
          fallbackCopyTextToClipboard(historyItem.txHex, button);
        });
      } else {
        // 降级到传统方法
        fallbackCopyTextToClipboard(historyItem.txHex, button);
      }
    };

    // 降级复制方法
    function fallbackCopyTextToClipboard(text, button) {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.top = "0";
      textArea.style.left = "0";
      textArea.style.position = "fixed";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        const successful = document.execCommand('copy');
        if (successful) {
          if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '✅ 已复制';
            button.style.background = '#28a745';
            setTimeout(() => {
              button.innerHTML = originalText;
              button.style.background = '#6c757d';
            }, 2000);
          } else {
            alert('交易十六进制已复制到剪贴板');
          }
        } else {
          alert('复制失败，请手动复制');
        }
      } catch (err) {
        console.error('降级复制方法也失败:', err);
        alert('复制失败，请手动复制');
      }
      
      document.body.removeChild(textArea);
    }

    // 删除单个历史记录
    window.deleteTransactionHistory = function(transactionId) {
      if (confirm('确定要删除这条交易记录吗？')) {
        const index = transactionHistory.findIndex(item => item.id == transactionId);
        if (index !== -1) {
          transactionHistory.splice(index, 1);
          saveTransactionHistory();
          updateTransactionHistoryDisplay();
        }
      }
    };

    // 一键提速全部交易
    window.batchSpeedUpAllTransactions = async function() {
      // 找到所有可提速的交易
      const speedUpableTransactions = transactionHistory.filter(item => 
        item.transactionData && !item.isSpeedUp
      );
      
      if (speedUpableTransactions.length === 0) {
        alert('没有可提速的交易');
        return;
      }
      
      const feeRateInput = prompt(
        `发现 ${speedUpableTransactions.length} 个可提速的交易\n\n请输入统一的新费率 (sat/vB)，必须高于原费率:`,
        '50'
      );
      
      if (!feeRateInput) return;
      
      const newFeeRate = parseFloat(feeRateInput);
      if (isNaN(newFeeRate) || newFeeRate <= 0) {
        alert('请输入有效的费率');
        return;
      }
      
      // 确认操作
      const confirmMessage = `即将对 ${speedUpableTransactions.length} 个交易进行提速：\n\n` +
        speedUpableTransactions.map(item => {
          const currentFeeRate = (item.transactionData.originalFee / 1150).toFixed(1);
          const modeText = item.testMode ? '测试' : '正常';
          return `• ${item.labitbuNumber} (${modeText}): ${currentFeeRate} → ${newFeeRate} sat/vB`;
        }).join('\n') + 
        `\n\n总费用增加约: ${speedUpableTransactions.reduce((sum, item) => {
          const oldFee = item.transactionData.originalFee;
          const newFee = Math.ceil(newFeeRate * 1150);
          return sum + (newFee - oldFee);
        }, 0)} sats\n\n确定继续吗？`;
      
      if (!confirm(confirmMessage)) return;
      
      // 显示进度
      const statusElement = document.getElementById('single-mint-status');
      let successCount = 0;
      let failCount = 0;
      
      statusElement.innerHTML = `<span style="color: #007bff;">⚡ 正在批量提速 ${speedUpableTransactions.length} 个交易...</span>`;
      
      for (let i = 0; i < speedUpableTransactions.length; i++) {
        const item = speedUpableTransactions[i];
        const currentFeeRate = (item.transactionData.originalFee / 1150).toFixed(1);
        
        try {
          // 检查费率是否比原费率高
          if (newFeeRate <= parseFloat(currentFeeRate)) {
            console.warn(`跳过交易 ${item.labitbuNumber}: 新费率 ${newFeeRate} 不高于原费率 ${currentFeeRate}`);
            failCount++;
            continue;
          }
          
          statusElement.innerHTML = `<span style="color: #007bff;">⚡ 正在提速 ${item.labitbuNumber} (${i + 1}/${speedUpableTransactions.length})...</span>`;
          
          // 重用单个提速的核心逻辑，但不显示UI
          await performSingleSpeedUpCore(item, newFeeRate);
          successCount++;
          
          console.log(`✅ ${item.labitbuNumber} 提速成功`);
          
        } catch (error) {
          console.error(`❌ ${item.labitbuNumber} 提速失败:`, error);
          failCount++;
        }
        
        // 添加延迟避免过于频繁的操作
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // 显示最终结果
      const finalMessage = `⚡ 批量提速完成！\n成功: ${successCount}, 失败: ${failCount}`;
      statusElement.innerHTML = `<span style="color: #28a745;">${finalMessage}</span>`;
      
      // 刷新交易历史显示
      updateTransactionHistoryDisplay();
    };

    // 单个提速核心逻辑（不包含UI更新）
    async function performSingleSpeedUpCore(historyItem, newFeeRate) {
      const txData = historyItem.transactionData;
      const newFee = Math.ceil(newFeeRate * 1150);

      // 重建钱包对象
      const wallet = {
        privateKey: Buffer.from(txData.wallet.privateKey),
        publicKey: Buffer.from(txData.wallet.publicKey)
      };

      // 确保 labitbuBytes 格式一致
      const labitbuBytesArray = Array.isArray(txData.labitbuBytes) ? 
        txData.labitbuBytes : 
        Array.from(txData.labitbuBytes);

      // 重新构建交易
      const psbtBytes = mint(
        txData.pubkeyHex,
        labitbuBytesArray,
        BigInt(txData.total),
        historyItem.targetAddress,
        BigInt(newFee),
        txData.inputs,
        txData.prevOuts
      );

      // 签名交易
      const psbt = bitcoin.Psbt.fromBuffer(Buffer.from(psbtBytes));
      for (let i = 0; i < txData.inputs.length; i++) {
        psbt.signInput(i, ECPair.fromPrivateKey(wallet.privateKey));
      }

      psbt.finalizeAllInputs();
      const signedTx = psbt.extractTransaction();
      const txHex = signedTx.toHex();
      const newTxId = signedTx.getId();

      // 广播新交易 (仅在正常模式下)
      if (!historyItem.testMode) {
        await broadcastTransaction(txHex);
      }

      // 更新历史记录
      if (!historyItem.speedUpHistory) {
        historyItem.speedUpHistory = [];
      }
      
      historyItem.speedUpHistory.push({
        timestamp: new Date(),
        oldFeeRate: parseFloat((txData.originalFee / 1150).toFixed(1)),
        newFeeRate: newFeeRate,
        newTxId: newTxId,
        newTxHex: txHex,
        oldTxId: historyItem.txId
      });
      
      // 更新原记录的最新交易信息
      historyItem.txId = newTxId;
      historyItem.txHex = txHex;
      historyItem.amount = txData.total - newFee;
      historyItem.transactionData.originalFee = newFee;
      
      // 保存更新后的历史
      saveTransactionHistory();
    }

    // 清空全部历史记录
    window.clearAllTransactionHistory = function() {
      if (confirm('确定要清空所有交易历史记录吗？此操作不可恢复！')) {
        transactionHistory.length = 0;
        saveTransactionHistory();
        updateTransactionHistoryDisplay();
      }
    };

    // 清除历史记录（内部使用）
    function clearTransactionHistory() {
      transactionHistory.length = 0;
      saveTransactionHistory();
      updateTransactionHistoryDisplay();
    }

    // RBF 提速功能
    window.speedUpTransaction = async function(transactionId) {
      const historyItem = transactionHistory.find(item => item.id == transactionId);
      if (!historyItem || !historyItem.transactionData) {
        alert('找不到交易数据或该交易不支持提速');
        return;
      }

      // 提示用户输入新的费率
      const currentFeeRate = (historyItem.transactionData.originalFee / 1150).toFixed(1);
      const modeText = historyItem.testMode ? '测试模式提速' : 'RBF提速';
      const newFeeRateStr = prompt(
        `${modeText}\n当前费率: ${currentFeeRate} sat/vB\n\n请输入新的费率 (sat/vB)，必须高于当前费率:`,
        (parseFloat(currentFeeRate) * 2).toFixed(1) // 默认建议翻倍
      );

      if (!newFeeRateStr) return;

      const newFeeRate = parseFloat(newFeeRateStr);
      const currentFeeRateNum = parseFloat(currentFeeRate);

      if (isNaN(newFeeRate) || newFeeRate <= currentFeeRateNum) {
        alert('新费率必须是有效数字且大于当前费率');
        return;
      }

      try {
        // 显示提速状态
        const statusEl = document.getElementById('single-mint-status');
        const statusText = historyItem.testMode ? '正在构建测试模式提速交易...' : '正在构建RBF提速交易...';
        statusEl.innerHTML = `<span style="color: #ff6b35;">⚡ ${statusText}</span>`;

        // 从历史数据重建交易
        const txData = historyItem.transactionData;
        const newFee = Math.ceil(newFeeRate * 1150);

        console.log(`🚀 RBF提速: ${currentFeeRate} → ${newFeeRate} sat/vB`);
        console.log(`🚀 费用变化: ${txData.originalFee} → ${newFee} sats`);

        // 重建钱包对象 - ECPair.fromPrivateKey 需要 Buffer 类型
        const wallet = {
          privateKey: Buffer.from(txData.wallet.privateKey),
          publicKey: Buffer.from(txData.wallet.publicKey)
        };
        
        console.log('🔍 重建的wallet.privateKey类型:', wallet.privateKey.constructor.name);
        console.log('🔍 重建的wallet.publicKey类型:', wallet.publicKey.constructor.name);

        // 确保 labitbuBytes 格式与正常铸造一致 - 使用 Array.from() 而不是 Uint8Array
        console.log('🔍 原始labitbuBytes类型:', txData.labitbuBytes.constructor?.name || typeof txData.labitbuBytes);
        console.log('🔍 labitbuBytes长度:', txData.labitbuBytes.length);
        console.log('🔍 原始labitbuBytes样本:', txData.labitbuBytes.slice(0, 10));
        
        // 确保格式与正常铸造保持一致：使用 Array.from()
        const labitbuBytesArray = Array.isArray(txData.labitbuBytes) ? 
          txData.labitbuBytes : 
          Array.from(txData.labitbuBytes);

        console.log('🔍 转换后类型:', labitbuBytesArray.constructor.name);
        console.log('🔍 转换后样本:', labitbuBytesArray.slice(0, 10));

        // 重新构建交易（使用与正常铸造相同的参数格式）
        const psbtBytes = mint(
          txData.pubkeyHex,
          labitbuBytesArray, // 使用普通数组，与正常铸造保持一致
          BigInt(txData.total),
          historyItem.targetAddress,
          BigInt(newFee),
          txData.inputs,
          txData.prevOuts
        );

        // 签名交易
        console.log('🔍 psbtBytes类型:', psbtBytes.constructor.name);
        console.log('🔍 psbtBytes长度:', psbtBytes.length);
        
        const psbt = bitcoin.Psbt.fromBuffer(Buffer.from(psbtBytes));
        
        for (let i = 0; i < txData.inputs.length; i++) {
          psbt.signInput(i, ECPair.fromPrivateKey(wallet.privateKey));
        }

        psbt.finalizeAllInputs();
        const signedTx = psbt.extractTransaction();
        const txHex = signedTx.toHex();
        const newTxId = signedTx.getId();

        // 广播新交易 (仅在正常模式下)
        let broadcastSuccess = false;
        let successText;
        if (historyItem.testMode) {
          successText = '测试模式提速交易构建成功！';
          statusEl.innerHTML = `<span style="color: #28a745;">✅ ${successText}</span>`;
        } else {
          try {
            statusEl.innerHTML = `<span style="color: #007bff;">📡 正在广播提速交易...</span>`;
            await broadcastTransaction(txHex);
            broadcastSuccess = true;
            successText = 'RBF提速交易广播成功！';
            statusEl.innerHTML = `<span style="color: #28a745;">✅ ${successText}</span>`;
          } catch (error) {
            console.error('RBF提速交易广播失败:', error);
            successText = 'RBF提速交易构建成功但广播失败！';
            statusEl.innerHTML = `<span style="color: #ff6b35;">⚠️ ${successText}</span>`;
          }
        }

        // 显示新的交易详情
        const speedUpDetails = document.createElement('div');
        const titleText = historyItem.testMode ? '🧪 测试模式提速交易' : '⚡ RBF 提速交易';
        const linkSection = historyItem.testMode ? 
          `<p><em>测试模式 - 交易未广播到网络</em></p>` :
          `<p><a href="https://mempool.space/tx/${newTxId}" target="_blank">在区块浏览器中查看新交易</a></p>`;
          
        speedUpDetails.innerHTML = `
          <div style="border: 2px solid #ff6b35; padding: 15px; margin: 10px 0; border-radius: 8px; background: #fff8f5;">
            <h4 style="color: #ff6b35; margin-top: 0;">${titleText}</h4>
            <p><strong>原交易ID:</strong> ${historyItem.txId}</p>
            <p><strong>新交易ID:</strong> ${newTxId}</p>
            <p><strong>费率提升:</strong> ${currentFeeRate} → ${newFeeRate} sat/vB</p>
            <p><strong>费用增加:</strong> ${newFee - txData.originalFee} sats</p>
            <p><strong>新交易十六进制:</strong></p>
            <textarea readonly style="width:100%;height:100px;font-family:monospace">${txHex}</textarea>
            ${linkSection}
          </div>
        `;
        
        statusEl.appendChild(speedUpDetails);

        // 更新原记录的提速历史，而不是创建新记录
        if (!historyItem.speedUpHistory) {
          historyItem.speedUpHistory = [];
        }
        
        historyItem.speedUpHistory.push({
          timestamp: new Date(),
          oldFeeRate: parseFloat(currentFeeRate),
          newFeeRate: newFeeRate,
          newTxId: newTxId,
          newTxHex: txHex,
          oldTxId: historyItem.txId
        });
        
        // 更新原记录的最新交易信息
        historyItem.txId = newTxId;
        historyItem.txHex = txHex;
        historyItem.amount = txData.total - newFee; // 更新输出金额
        historyItem.transactionData.originalFee = newFee; // 更新费用用于下次提速
        
        // 保存更新后的历史
        saveTransactionHistory();
        updateTransactionHistoryDisplay();

      } catch (error) {
        console.error('RBF提速失败:', error);
        document.getElementById('single-mint-status').innerHTML = 
          `<span style="color: red;">❌ RBF提速失败: ${error.message}</span>`;
      }
    };

    // 派生路径选择处理
    window.updateDerivationPath = function() {
      const select = document.getElementById('derivation-path-select');
      const input = document.getElementById('derivation-path');
      
      if (select.value === 'custom') {
        input.style.display = 'block';
        input.focus();
      } else {
        input.style.display = 'none';
        input.value = select.value;
      }
      
      // 保存派生路径
      saveToStorage(STORAGE_KEYS.DERIVATION_PATH, input.value);
    };

    // 设置相关事件监听器
    function initializeSettingsListeners() {
      // 测试模式切换
      const testModeCheckbox = document.getElementById('test-mode-checkbox');
      testModeCheckbox.addEventListener('change', (e) => {
        testMode = e.target.checked;
        document.getElementById('test-mode-info').style.display = testMode ? 'block' : 'none';
        saveToStorage(STORAGE_KEYS.TEST_MODE, testMode.toString());
        console.log('测试模式:', testMode ? '开启' : '关闭');
      });

      // 自定义费率变化
      const customFeeRateInput = document.getElementById('custom-fee-rate');
      customFeeRateInput.addEventListener('input', (e) => {
        customFeeRate = parseFloat(e.target.value) || 20;
        saveToStorage(STORAGE_KEYS.CUSTOM_FEE_RATE, customFeeRate.toString());
      });

      // 获取推荐费率按钮
      document.getElementById('refresh-fee-btn').addEventListener('click', async () => {
        try {
          const feeRate = await fees.getFeesRecommended();
          const recommendedFee = feeRate.fastestFee; // 保持小数
          customFeeRateInput.value = recommendedFee;
          customFeeRate = recommendedFee;
          saveToStorage(STORAGE_KEYS.CUSTOM_FEE_RATE, recommendedFee.toString());
          console.log('更新推荐费率:', recommendedFee, 'sat/vB');
        } catch (error) {
          console.error('获取推荐费率失败:', error);
        }
      });

      // 助记词保存
      mnemonicInput.addEventListener('input', () => {
        saveToStorage(STORAGE_KEYS.MNEMONIC, mnemonicInput.value);
      });

      // 派生路径保存
      document.getElementById('derivation-path').addEventListener('input', (e) => {
        saveToStorage(STORAGE_KEYS.DERIVATION_PATH, e.target.value);
      });

      // 目标地址保存
      if (destinationAddress) {
        destinationAddress.addEventListener('input', () => {
          saveToStorage(STORAGE_KEYS.DESTINATION_ADDRESS, destinationAddress.value);
        });
      }
    }

    // 直接绑定选项卡事件（在模块加载完成后）
    document.getElementById('tab-mnemonic').onclick = () => switchTab('mnemonic');
    document.getElementById('tab-private-key').onclick = () => switchTab('private-key');
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化设置相关监听器
    initializeSettingsListeners();
    
    // 清除输入按钮
    clearInputsBtn.onclick = () => {
      if (confirm('确定要清除所有输入数据和保存的信息吗？')) {
        // 清除输入框
        mnemonicInput.value = '';
        privateKeyInput.value = '';
        document.getElementById('derivation-path').value = 'm/84\'/0\'/0\'/0/0';
        document.getElementById('derivation-path-select').value = 'm/84\'/0\'/0\'/0/0';
        if (destinationAddress) destinationAddress.value = '';
        
        // 清除显示
        document.getElementById('wallet-info').style.display = 'none';
        document.getElementById('single-mint-section').style.display = 'none';
        document.getElementById('batch-mint-section').style.display = 'none';
        
        // 清除localStorage
        clearStorage();
        
        // 重置状态
        currentWallet = null;
        currentLabitbuBytes = null;
        currentDepositAddress = null;
        clearTransactionHistory();
        
        console.log('已清除所有数据');
      }
    };
    
    // 初始化时隐藏相关部分
    document.getElementById('wallet-info').style.display = 'none';
    document.getElementById('single-mint-section').style.display = 'none';
    document.getElementById('batch-mint-section').style.display = 'none';
    
    // 加载保存的数据
    loadSavedData();
    
    // 加载交易历史
    loadTransactionHistory();
    
    console.log('🚀 应用初始化完成');

  </script>
</body>
</html>