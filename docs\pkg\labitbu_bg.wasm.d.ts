/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const generate_labitbu_bytes: (a: number, b: number, c: number, d: number, e: number) => void;
export const mint: (a: number, b: number, c: number, d: number, e: number, f: bigint, g: number, h: number, i: bigint, j: number, k: number) => void;
export const create_deposit_address: (a: number, b: number, c: number, d: number, e: number) => void;
export const rustsecp256k1_v0_10_0_context_create: (a: number) => number;
export const rustsecp256k1_v0_10_0_context_destroy: (a: number) => void;
export const rustsecp256k1_v0_10_0_default_illegal_callback_fn: (a: number, b: number) => void;
export const rustsecp256k1_v0_10_0_default_error_callback_fn: (a: number, b: number) => void;
export const __wbindgen_export_0: (a: number, b: number) => number;
export const __wbindgen_export_1: (a: number, b: number, c: number, d: number) => number;
export const __wbindgen_export_2: (a: number) => void;
export const __wbindgen_add_to_stack_pointer: (a: number) => number;
export const __wbindgen_export_3: (a: number, b: number, c: number) => void;
